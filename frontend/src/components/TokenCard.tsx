import React from 'react';
import { ProcessedTokenData } from '../types/token';
import { ProgressBar } from './ProgressBar';
import './TokenCard.css';

interface TokenCardProps {
  token: ProcessedTokenData;
  category: 'new' | 'bonding' | 'bonded';
}

export const TokenCard: React.FC<TokenCardProps> = ({ token, category }) => {
  const formatNumber = (num: number): string => {
    if (num >= 1e9) return `${(num / 1e9).toFixed(2)}B`;
    if (num >= 1e6) return `${(num / 1e6).toFixed(2)}M`;
    if (num >= 1e3) return `${(num / 1e3).toFixed(2)}K`;
    return num.toFixed(2);
  };

  const formatPrice = (price: number): string => {
    if (price < 0.000001) return price.toExponential(2);
    if (price < 0.01) return price.toFixed(6);
    return price.toFixed(4);
  };

  const formatTimeAgo = (date: Date | null): string => {
    if (!date) return 'Unknown';
    
    const now = new Date();
    const diffMs = now.getTime() - new Date(date).getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    return `${diffDays}d ago`;
  };

  const getPriceChangeColor = (change: number): string => {
    if (change > 0) return 'positive';
    if (change < 0) return 'negative';
    return 'neutral';
  };

  const getCategoryBadgeColor = (cat: string): string => {
    switch (cat) {
      case 'new': return 'badge--new';
      case 'bonding': return 'badge--bonding';
      case 'bonded': return 'badge--bonded';
      default: return 'badge--default';
    }
  };

  return (
    <div className={`token-card token-card--${category}`}>
      <div className="token-card__header">
        <div className="token-card__token-info">
          <div className="token-card__image-container">
            {token.image ? (
              <img 
                src={token.image} 
                alt={`${token.symbol} logo`}
                className="token-card__image"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.style.display = 'none';
                }}
              />
            ) : (
              <div className="token-card__image-placeholder">
                {token.symbol.charAt(0)}
              </div>
            )}
          </div>
          <div className="token-card__names">
            <h3 className="token-card__name" title={token.name}>
              {token.name}
            </h3>
            <p className="token-card__symbol">{token.symbol}</p>
          </div>
        </div>
        <div className={`token-card__badge ${getCategoryBadgeColor(category)}`}>
          {category.toUpperCase()}
        </div>
      </div>

      <div className="token-card__metrics">
        <div className="token-card__metric">
          <span className="token-card__metric-label">Price</span>
          <span className="token-card__metric-value">
            ${formatPrice(token.price)}
          </span>
        </div>

        <div className="token-card__metric">
          <span className="token-card__metric-label">24h Change</span>
          <span className={`token-card__metric-value token-card__price-change--${getPriceChangeColor(token.priceChange24h)}`}>
            {token.priceChange24h > 0 ? '+' : ''}{token.priceChange24h.toFixed(2)}%
          </span>
        </div>

        <div className="token-card__metric">
          <span className="token-card__metric-label">Market Cap</span>
          <span className="token-card__metric-value">
            ${formatNumber(token.marketCap)}
          </span>
        </div>

        <div className="token-card__metric">
          <span className="token-card__metric-label">Volume (1h)</span>
          <span className="token-card__metric-value">
            ${formatNumber(token.volume)}
          </span>
        </div>

        <div className="token-card__metric">
          <span className="token-card__metric-label">Transactions (1h)</span>
          <span className="token-card__metric-value">
            {formatNumber(token.transactionCount)}
          </span>
        </div>

        <div className="token-card__metric">
          <span className="token-card__metric-label">Holders</span>
          <span className="token-card__metric-value">
            {formatNumber(token.holders)}
          </span>
        </div>

        {category === 'bonding' && (
          <div className="token-card__metric token-card__metric--full">
            <span className="token-card__metric-label">Bonding Progress</span>
            <div className="token-card__progress-container">
              <ProgressBar 
                percentage={token.bondingPercentage} 
                height={16}
                animated={true}
                color={token.bondingPercentage > 80 ? 'success' : token.bondingPercentage > 50 ? 'warning' : 'info'}
              />
            </div>
          </div>
        )}

        {token.createdAt && (
          <div className="token-card__metric">
            <span className="token-card__metric-label">Created</span>
            <span className="token-card__metric-value token-card__time">
              {formatTimeAgo(token.createdAt)}
            </span>
          </div>
        )}
      </div>

      <div className="token-card__footer">
        <button 
          className="token-card__action-btn"
          onClick={() => {
            // Copy token address to clipboard
            navigator.clipboard.writeText(token.address);
          }}
          title="Copy address"
        >
          📋 Copy Address
        </button>
        <button 
          className="token-card__action-btn token-card__action-btn--primary"
          onClick={() => {
            // Open in new tab (could be DEX screener or similar)
            window.open(`https://dexscreener.com/solana/${token.address}`, '_blank');
          }}
          title="View on DEX Screener"
        >
          📊 View Chart
        </button>
      </div>
    </div>
  );
};
