import React, { useState } from 'react';
import { CategorizedTokenData, ProcessedTokenData } from '../types/token';
import { TokenCard } from './TokenCard';
import './TokenTable.css';

interface TokenTableProps {
  tokenData: CategorizedTokenData;
  isLoading?: boolean;
}

export const TokenTable: React.FC<TokenTableProps> = ({ tokenData, isLoading = false }) => {
  const [activeTab, setActiveTab] = useState<'new' | 'bonding' | 'bonded'>('new');
  const [searchTerm, setSearchTerm] = useState('');

  const filterTokens = (tokens: ProcessedTokenData[]): ProcessedTokenData[] => {
    if (!searchTerm.trim()) return tokens;
    
    const term = searchTerm.toLowerCase();
    return tokens.filter(token => 
      token.name.toLowerCase().includes(term) ||
      token.symbol.toLowerCase().includes(term) ||
      token.address.toLowerCase().includes(term)
    );
  };

  const getTabCount = (category: keyof CategorizedTokenData): number => {
    return tokenData[category]?.length || 0;
  };

  const getTabTitle = (category: keyof CategorizedTokenData): string => {
    const count = getTabCount(category);
    const categoryNames = {
      new: 'New Tokens',
      bonding: 'Bonding Curve',
      bonded: 'Bonded Tokens'
    };
    return `${categoryNames[category]} (${count})`;
  };

  const renderTokenList = (tokens: ProcessedTokenData[], category: 'new' | 'bonding' | 'bonded') => {
    const filteredTokens = filterTokens(tokens);

    if (isLoading) {
      return (
        <div className="token-table__loading">
          <div className="token-table__loading-spinner"></div>
          <p>Loading tokens...</p>
        </div>
      );
    }

    if (filteredTokens.length === 0) {
      return (
        <div className="token-table__empty">
          {searchTerm ? (
            <>
              <p>No tokens found matching "{searchTerm}"</p>
              <button 
                className="token-table__clear-search"
                onClick={() => setSearchTerm('')}
              >
                Clear search
              </button>
            </>
          ) : (
            <p>No {category} tokens available</p>
          )}
        </div>
      );
    }

    return (
      <div className="token-table__list">
        {filteredTokens.map((token) => (
          <TokenCard 
            key={token.address} 
            token={token} 
            category={category}
          />
        ))}
      </div>
    );
  };

  return (
    <div className="token-table">
      <div className="token-table__header">
        <h1 className="token-table__title">Token Monitor</h1>
        <div className="token-table__search">
          <input
            type="text"
            placeholder="Search tokens by name, symbol, or address..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="token-table__search-input"
          />
          {searchTerm && (
            <button
              className="token-table__search-clear"
              onClick={() => setSearchTerm('')}
              title="Clear search"
            >
              ✕
            </button>
          )}
        </div>
      </div>

      <div className="token-table__tabs">
        <button
          className={`token-table__tab ${activeTab === 'new' ? 'token-table__tab--active' : ''}`}
          onClick={() => setActiveTab('new')}
        >
          {getTabTitle('new')}
        </button>
        <button
          className={`token-table__tab ${activeTab === 'bonding' ? 'token-table__tab--active' : ''}`}
          onClick={() => setActiveTab('bonding')}
        >
          {getTabTitle('bonding')}
        </button>
        <button
          className={`token-table__tab ${activeTab === 'bonded' ? 'token-table__tab--active' : ''}`}
          onClick={() => setActiveTab('bonded')}
        >
          {getTabTitle('bonded')}
        </button>
      </div>

      <div className="token-table__content">
        <div className="token-table__tab-content">
          {renderTokenList(tokenData[activeTab] || [], activeTab)}
        </div>
      </div>

      <div className="token-table__stats">
        <div className="token-table__stat">
          <span className="token-table__stat-label">Total Tokens:</span>
          <span className="token-table__stat-value">
            {Object.values(tokenData).reduce((sum, tokens) => sum + tokens.length, 0)}
          </span>
        </div>
        <div className="token-table__stat">
          <span className="token-table__stat-label">Active Tab:</span>
          <span className="token-table__stat-value">
            {getTabCount(activeTab)} {activeTab} tokens
          </span>
        </div>
        {searchTerm && (
          <div className="token-table__stat">
            <span className="token-table__stat-label">Filtered:</span>
            <span className="token-table__stat-value">
              {filterTokens(tokenData[activeTab] || []).length} results
            </span>
          </div>
        )}
      </div>
    </div>
  );
};
