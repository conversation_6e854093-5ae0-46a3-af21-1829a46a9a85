import React from 'react';
import { ConnectionStatus as ConnectionStatusType } from '../types/token';
import './ConnectionStatus.css';

interface ConnectionStatusProps {
  status: ConnectionStatusType;
  onReconnect: () => void;
}

export const ConnectionStatus: React.FC<ConnectionStatusProps> = ({ status, onReconnect }) => {
  const getStatusColor = (): string => {
    if (status.isConnected) return 'success';
    if (status.isConnecting) return 'warning';
    if (status.error) return 'error';
    return 'disconnected';
  };

  const getStatusText = (): string => {
    if (status.isConnected) return 'Connected';
    if (status.isConnecting) return 'Connecting...';
    if (status.error) return 'Connection Error';
    return 'Disconnected';
  };

  const getStatusIcon = (): string => {
    if (status.isConnected) return '🟢';
    if (status.isConnecting) return '🟡';
    if (status.error) return '🔴';
    return '⚫';
  };

  const formatLastConnected = (): string => {
    if (!status.lastConnected) return 'Never';
    
    const now = new Date();
    const diff = now.getTime() - status.lastConnected.getTime();
    const minutes = Math.floor(diff / 60000);
    const seconds = Math.floor((diff % 60000) / 1000);
    
    if (minutes > 0) return `${minutes}m ${seconds}s ago`;
    return `${seconds}s ago`;
  };

  return (
    <div className={`connection-status connection-status--${getStatusColor()}`}>
      <div className="connection-status__main">
        <span className="connection-status__icon">{getStatusIcon()}</span>
        <span className="connection-status__text">{getStatusText()}</span>
        {!status.isConnected && !status.isConnecting && (
          <button 
            className="connection-status__reconnect"
            onClick={onReconnect}
            title="Reconnect to server"
          >
            🔄 Reconnect
          </button>
        )}
      </div>
      
      <div className="connection-status__details">
        {status.lastConnected && (
          <span className="connection-status__detail">
            Last connected: {formatLastConnected()}
          </span>
        )}
        
        {status.reconnectAttempts > 0 && (
          <span className="connection-status__detail">
            Reconnect attempts: {status.reconnectAttempts}
          </span>
        )}
        
        {status.error && (
          <span className="connection-status__error">
            Error: {status.error}
          </span>
        )}
      </div>
    </div>
  );
};
