import React from 'react';
import { useWebSocket } from './hooks/useWebSocket';
import { TokenTable } from './components/TokenTable';
import { ConnectionStatus } from './components/ConnectionStatus';
import './App.css';

const WEBSOCKET_URL = 'ws://localhost:3001';

function App() {
  const { tokenData, connectionStatus, reconnect } = useWebSocket({
    url: WEBSOCKET_URL,
    reconnectAttempts: 5,
    reconnectDelay: 3000
  });

  return (
    <div className="app">
      <ConnectionStatus
        status={connectionStatus}
        onReconnect={reconnect}
      />

      <main className="app__main">
        <TokenTable
          tokenData={tokenData}
          isLoading={connectionStatus.isConnecting && !connectionStatus.isConnected}
        />
      </main>

      <footer className="app__footer">
        <p>Real-time Token Monitoring System</p>
        <p>Powered by Mobula API • Built with React & WebSocket</p>
      </footer>
    </div>
  );
}

export default App;
