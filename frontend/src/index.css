:root {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: #212529;
  background-color: #ffffff;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  min-width: 320px;
  min-height: 100vh;
  background: #f8f9fa;
}

#root {
  min-height: 100vh;
}

a {
  font-weight: 500;
  color: #007bff;
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: #0056b3;
  text-decoration: underline;
}

h1, h2, h3, h4, h5, h6 {
  margin: 0;
  font-weight: 600;
  line-height: 1.2;
}

p {
  margin: 0;
  line-height: 1.5;
}

button {
  border-radius: 6px;
  border: 1px solid #dee2e6;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  font-family: inherit;
  background-color: #ffffff;
  color: #495057;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

button:hover {
  background-color: #f8f9fa;
  border-color: #adb5bd;
  transform: translateY(-1px);
}

button:focus,
button:focus-visible {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

input {
  font-family: inherit;
  font-size: 14px;
  border-radius: 6px;
  border: 1px solid #dee2e6;
  padding: 8px 12px;
  transition: border-color 0.2s ease;
}

input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  :root {
    color: #e2e8f0;
    background-color: #1a202c;
  }

  body {
    background: #1a202c;
  }

  button {
    background-color: #2d3748;
    color: #e2e8f0;
    border-color: #4a5568;
  }

  button:hover {
    background-color: #4a5568;
    border-color: #718096;
  }

  input {
    background-color: #2d3748;
    color: #e2e8f0;
    border-color: #4a5568;
  }

  input:focus {
    border-color: #63b3ed;
    box-shadow: 0 0 0 3px rgba(99, 179, 237, 0.1);
  }

  ::-webkit-scrollbar-track {
    background: #2d3748;
  }

  ::-webkit-scrollbar-thumb {
    background: #4a5568;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #718096;
  }
}

/* Utility classes */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
