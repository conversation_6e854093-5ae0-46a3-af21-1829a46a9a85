import { useState, useEffect, useRef, useCallback } from 'react';
import { CategorizedTokenData, WebSocketMessage, ConnectionStatus, ProcessedTokenData } from '../types/token';

interface UseWebSocketOptions {
  url: string;
  reconnectAttempts?: number;
  reconnectDelay?: number;
}

interface UseWebSocketReturn {
  tokenData: CategorizedTokenData;
  connectionStatus: ConnectionStatus;
  sendMessage: (message: WebSocketMessage) => void;
  reconnect: () => void;
  disconnect: () => void;
}

export const useWebSocket = (options: UseWebSocketOptions): UseWebSocketReturn => {
  const { url, reconnectAttempts = 5, reconnectDelay = 3000 } = options;
  
  const [tokenData, setTokenData] = useState<CategorizedTokenData>({
    new: [],
    bonding: [],
    bonded: []
  });
  
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>({
    isConnected: false,
    isConnecting: false,
    lastConnected: null,
    reconnectAttempts: 0,
    error: null
  });

  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const shouldReconnectRef = useRef(true);

  const connect = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN || connectionStatus.isConnecting) {
      return;
    }

    console.log('Connecting to WebSocket:', url);
    
    setConnectionStatus(prev => ({
      ...prev,
      isConnecting: true,
      error: null
    }));

    try {
      const ws = new WebSocket(url);
      wsRef.current = ws;

      ws.onopen = () => {
        console.log('WebSocket connected');
        setConnectionStatus(prev => ({
          ...prev,
          isConnected: true,
          isConnecting: false,
          lastConnected: new Date(),
          reconnectAttempts: 0,
          error: null
        }));

        // Request initial data
        ws.send(JSON.stringify({ type: 'get-data' }));
      };

      ws.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data);
          handleMessage(message);
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };

      ws.onclose = (event) => {
        console.log('WebSocket disconnected:', event.code, event.reason);
        wsRef.current = null;
        
        setConnectionStatus(prev => ({
          ...prev,
          isConnected: false,
          isConnecting: false,
          error: event.reason || 'Connection closed'
        }));

        // Attempt to reconnect if should reconnect and haven't exceeded max attempts
        if (shouldReconnectRef.current && connectionStatus.reconnectAttempts < reconnectAttempts) {
          scheduleReconnect();
        }
      };

      ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        setConnectionStatus(prev => ({
          ...prev,
          isConnected: false,
          isConnecting: false,
          error: 'Connection error'
        }));
      };

    } catch (error) {
      console.error('Failed to create WebSocket connection:', error);
      setConnectionStatus(prev => ({
        ...prev,
        isConnecting: false,
        error: 'Failed to create connection'
      }));
    }
  }, [url, connectionStatus.isConnecting, connectionStatus.reconnectAttempts, reconnectAttempts]);

  const scheduleReconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }

    const delay = reconnectDelay * Math.pow(2, connectionStatus.reconnectAttempts);
    console.log(`Scheduling reconnect in ${delay}ms (attempt ${connectionStatus.reconnectAttempts + 1}/${reconnectAttempts})`);

    setConnectionStatus(prev => ({
      ...prev,
      reconnectAttempts: prev.reconnectAttempts + 1
    }));

    reconnectTimeoutRef.current = setTimeout(() => {
      if (shouldReconnectRef.current) {
        connect();
      }
    }, delay);
  }, [connect, reconnectDelay, connectionStatus.reconnectAttempts, reconnectAttempts]);

  const handleMessage = useCallback((message: WebSocketMessage) => {
    console.log('Received message:', message.type);

    switch (message.type) {
      case 'initial-data':
      case 'current-data':
      case 'data-initialized':
      case 'data-synced':
      case 'data-updated':
        if (message.payload) {
          setTokenData(message.payload);
        }
        break;

      case 'token-added':
        if (message.payload?.token) {
          const { token, category } = message.payload;
          setTokenData(prev => ({
            ...prev,
            [category]: [token, ...prev[category as keyof CategorizedTokenData]].slice(0, 50)
          }));
        }
        break;

      case 'token-updated':
        if (message.payload?.token) {
          const { token } = message.payload;
          setTokenData(prev => {
            const newData = { ...prev };
            
            // Remove from all categories
            Object.keys(newData).forEach(cat => {
              const categoryKey = cat as keyof CategorizedTokenData;
              newData[categoryKey] = newData[categoryKey].filter(t => t.address !== token.address);
            });
            
            // Add to correct category
            newData[token.category] = [token, ...newData[token.category]].slice(0, 50);
            
            return newData;
          });
        }
        break;

      case 'token-removed':
        if (message.payload?.address) {
          const { address } = message.payload;
          setTokenData(prev => {
            const newData = { ...prev };
            Object.keys(newData).forEach(cat => {
              const categoryKey = cat as keyof CategorizedTokenData;
              newData[categoryKey] = newData[categoryKey].filter(t => t.address !== address);
            });
            return newData;
          });
        }
        break;

      case 'mobula-status':
        console.log('Mobula status:', message.payload?.status);
        break;

      case 'error':
        console.error('Server error:', message.payload);
        setConnectionStatus(prev => ({
          ...prev,
          error: message.payload?.message || 'Server error'
        }));
        break;

      case 'pong':
        // Handle pong response
        break;

      default:
        console.log('Unknown message type:', message.type);
    }
  }, []);

  const sendMessage = useCallback((message: WebSocketMessage) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket not connected, cannot send message');
    }
  }, []);

  const reconnect = useCallback(() => {
    setConnectionStatus(prev => ({
      ...prev,
      reconnectAttempts: 0,
      error: null
    }));
    
    if (wsRef.current) {
      wsRef.current.close();
    }
    
    connect();
  }, [connect]);

  const disconnect = useCallback(() => {
    shouldReconnectRef.current = false;
    
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    
    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }
    
    setConnectionStatus(prev => ({
      ...prev,
      isConnected: false,
      isConnecting: false
    }));
  }, []);

  // Initialize connection on mount
  useEffect(() => {
    shouldReconnectRef.current = true;
    connect();

    // Cleanup on unmount
    return () => {
      shouldReconnectRef.current = false;
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, [connect]);

  // Ping mechanism to keep connection alive
  useEffect(() => {
    if (!connectionStatus.isConnected) return;

    const pingInterval = setInterval(() => {
      sendMessage({ type: 'ping', timestamp: Date.now() });
    }, 30000); // 30 seconds

    return () => clearInterval(pingInterval);
  }, [connectionStatus.isConnected, sendMessage]);

  return {
    tokenData,
    connectionStatus,
    sendMessage,
    reconnect,
    disconnect
  };
};
