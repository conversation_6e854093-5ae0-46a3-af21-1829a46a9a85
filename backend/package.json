{"name": "backend", "version": "1.0.0", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "nodemon src/index.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@types/ws": "^8.18.1", "dotenv": "^17.2.1", "express": "^5.1.0", "node-cache": "^5.1.2", "ws": "^8.18.3"}, "devDependencies": {"@types/express": "^5.0.3", "@types/node": "^24.1.0", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}