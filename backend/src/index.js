"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const dotenv_1 = __importDefault(require("dotenv"));
const mobulaClient_js_1 = require("./services/mobulaClient.js");
const dataCache_js_1 = require("./services/dataCache.js");
const dataProcessor_js_1 = require("./services/dataProcessor.js");
const websocketServer_js_1 = require("./services/websocketServer.js");
// Load environment variables
dotenv_1.default.config();
const app = (0, express_1.default)();
const port = parseInt(process.env.PORT || '3000');
const wsPort = parseInt(process.env.WS_PORT || '3001');
// Middleware
app.use(express_1.default.json());
app.use(express_1.default.static('public'));
// CORS middleware for development
app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
    if (req.method === 'OPTIONS') {
        res.sendStatus(200);
    }
    else {
        next();
    }
});
// Initialize services
const mobulaApiKey = process.env.MOBULA_API_KEY;
const mobulaWsUrl = process.env.MOBULA_WS_URL || 'wss://pulse-v2-api.mobula.io';
if (!mobulaApiKey) {
    console.error('MOBULA_API_KEY environment variable is required');
    process.exit(1);
}
console.log('Initializing services...');
// Create service instances
const mobulaClient = new mobulaClient_js_1.MobulaWebSocketClient(mobulaApiKey, mobulaWsUrl);
const dataCache = new dataCache_js_1.DataCache();
const dataProcessor = new dataProcessor_js_1.DataProcessor(mobulaClient, dataCache);
const wsServer = new websocketServer_js_1.TokenWebSocketServer(dataProcessor, wsPort);
// REST API endpoints
app.get('/', (_req, res) => {
    res.json({
        message: 'Token Monitoring System API',
        status: 'running',
        endpoints: {
            '/api/tokens': 'Get all categorized tokens',
            '/api/tokens/:category': 'Get tokens by category (new, bonding, bonded)',
            '/api/stats': 'Get system statistics',
            '/api/health': 'Health check'
        },
        websocket: {
            port: wsPort,
            url: `ws://localhost:${wsPort}`
        }
    });
});
app.get('/api/health', (_req, res) => {
    const stats = {
        api: 'healthy',
        dataProcessor: dataProcessor.isRunning(),
        websocketServer: wsServer.getClientCount(),
        mobula: dataProcessor.getStats().mobulaConnectionState,
        timestamp: new Date().toISOString()
    };
    res.json(stats);
});
app.get('/api/tokens', (_req, res) => {
    try {
        const categorizedData = dataProcessor.getCategorizedData();
        res.json({
            success: true,
            data: categorizedData,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('Error getting tokens:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to get tokens',
            timestamp: new Date().toISOString()
        });
    }
});
app.get('/api/tokens/:category', (req, res) => {
    try {
        const category = req.params.category;
        if (!['new', 'bonding', 'bonded'].includes(category)) {
            return res.status(400).json({
                success: false,
                error: 'Invalid category. Must be one of: new, bonding, bonded',
                timestamp: new Date().toISOString()
            });
        }
        const tokens = dataProcessor.getTokensByCategory(category);
        res.json({
            success: true,
            category,
            data: tokens,
            count: tokens.length,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('Error getting tokens by category:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to get tokens by category',
            timestamp: new Date().toISOString()
        });
    }
});
app.get('/api/stats', (_req, res) => {
    try {
        const processorStats = dataProcessor.getStats();
        const serverStats = wsServer.getStats();
        res.json({
            success: true,
            data: {
                processor: processorStats,
                websocketServer: serverStats,
                uptime: process.uptime(),
                memory: process.memoryUsage(),
                timestamp: new Date().toISOString()
            }
        });
    }
    catch (error) {
        console.error('Error getting stats:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to get stats',
            timestamp: new Date().toISOString()
        });
    }
});
// Error handling middleware
app.use((error, _req, res, _next) => {
    console.error('Express error:', error);
    res.status(500).json({
        success: false,
        error: 'Internal server error',
        timestamp: new Date().toISOString()
    });
});
// Start services
function startServices() {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            console.log('Starting Token Monitoring System...');
            // Start data processor (connects to Mobula)
            dataProcessor.start();
            // Start WebSocket server
            wsServer.start();
            // Start HTTP server
            app.listen(port, () => {
                console.log(`✅ HTTP API server running on http://localhost:${port}`);
                console.log(`✅ WebSocket server running on ws://localhost:${wsPort}`);
                console.log(`✅ Token Monitoring System is ready!`);
                console.log(`\nAPI Endpoints:`);
                console.log(`  GET  http://localhost:${port}/api/health`);
                console.log(`  GET  http://localhost:${port}/api/tokens`);
                console.log(`  GET  http://localhost:${port}/api/tokens/:category`);
                console.log(`  GET  http://localhost:${port}/api/stats`);
                console.log(`\nWebSocket: ws://localhost:${wsPort}`);
            });
        }
        catch (error) {
            console.error('Failed to start services:', error);
            process.exit(1);
        }
    });
}
// Graceful shutdown
process.on('SIGINT', () => {
    console.log('\nShutting down gracefully...');
    dataProcessor.stop();
    wsServer.stop();
    process.exit(0);
});
process.on('SIGTERM', () => {
    console.log('\nShutting down gracefully...');
    dataProcessor.stop();
    wsServer.stop();
    process.exit(0);
});
// Start the application
startServices();
