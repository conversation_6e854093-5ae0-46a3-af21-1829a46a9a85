"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.TokenWebSocketServer = void 0;
const ws_1 = __importStar(require("ws"));
const events_1 = require("events");
class TokenWebSocketServer extends events_1.EventEmitter {
    constructor(dataProcessor, port) {
        super();
        this.wss = null;
        this.clients = new Map();
        this.heartbeatInterval = null;
        this.dataProcessor = dataProcessor;
        this.port = port;
        this.setupDataProcessorHandlers();
    }
    /**
     * Start the WebSocket server
     */
    start() {
        console.log(`Starting WebSocket server on port ${this.port}...`);
        this.wss = new ws_1.WebSocketServer({
            port: this.port,
            perMessageDeflate: false // Disable compression for better performance
        });
        this.wss.on('connection', (ws, request) => {
            this.handleNewConnection(ws, request);
        });
        this.wss.on('error', (error) => {
            console.error('WebSocket server error:', error);
            this.emit('server-error', error);
        });
        // Start heartbeat mechanism
        this.startHeartbeat();
        console.log(`WebSocket server started on port ${this.port}`);
        this.emit('server-started');
    }
    /**
     * Stop the WebSocket server
     */
    stop() {
        console.log('Stopping WebSocket server...');
        // Stop heartbeat
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
        // Close all client connections
        this.clients.forEach((client) => {
            client.ws.close();
        });
        this.clients.clear();
        // Close server
        if (this.wss) {
            this.wss.close(() => {
                console.log('WebSocket server stopped');
                this.emit('server-stopped');
            });
            this.wss = null;
        }
    }
    /**
     * Handle new client connection
     */
    handleNewConnection(ws, request) {
        const clientId = this.generateClientId();
        const client = {
            id: clientId,
            ws,
            isAlive: true,
            connectedAt: new Date()
        };
        this.clients.set(clientId, client);
        console.log(`New client connected: ${clientId} (Total: ${this.clients.size})`);
        // Set up client event handlers
        ws.on('message', (data) => {
            this.handleClientMessage(clientId, data);
        });
        ws.on('close', (code, reason) => {
            console.log(`Client ${clientId} disconnected: ${code} - ${reason.toString()}`);
            this.clients.delete(clientId);
            this.emit('client-disconnected', { clientId, code, reason: reason.toString() });
        });
        ws.on('error', (error) => {
            console.error(`Client ${clientId} error:`, error);
            this.clients.delete(clientId);
            this.emit('client-error', { clientId, error });
        });
        ws.on('pong', () => {
            client.isAlive = true;
        });
        // Send initial data to the new client
        this.sendInitialData(clientId);
        this.emit('client-connected', { clientId });
    }
    /**
     * Handle messages from clients
     */
    handleClientMessage(clientId, data) {
        try {
            const message = JSON.parse(data.toString());
            console.log(`Message from client ${clientId}:`, message.type);
            switch (message.type) {
                case 'ping':
                    this.sendToClient(clientId, { type: 'pong', timestamp: Date.now() });
                    break;
                case 'get-data':
                    this.sendCurrentData(clientId);
                    break;
                case 'get-stats':
                    this.sendStats(clientId);
                    break;
                default:
                    console.log(`Unknown message type from client ${clientId}:`, message.type);
            }
        }
        catch (error) {
            console.error(`Error parsing message from client ${clientId}:`, error);
        }
    }
    /**
     * Send initial data to a client
     */
    sendInitialData(clientId) {
        const categorizedData = this.dataProcessor.getCategorizedData();
        this.sendToClient(clientId, {
            type: 'initial-data',
            payload: categorizedData,
            timestamp: Date.now()
        });
    }
    /**
     * Send current data to a client
     */
    sendCurrentData(clientId) {
        const categorizedData = this.dataProcessor.getCategorizedData();
        this.sendToClient(clientId, {
            type: 'current-data',
            payload: categorizedData,
            timestamp: Date.now()
        });
    }
    /**
     * Send stats to a client
     */
    sendStats(clientId) {
        const stats = this.dataProcessor.getStats();
        this.sendToClient(clientId, {
            type: 'stats',
            payload: stats,
            timestamp: Date.now()
        });
    }
    /**
     * Send message to a specific client
     */
    sendToClient(clientId, message) {
        const client = this.clients.get(clientId);
        if (!client || client.ws.readyState !== ws_1.default.OPEN) {
            return;
        }
        try {
            client.ws.send(JSON.stringify(message));
        }
        catch (error) {
            console.error(`Error sending message to client ${clientId}:`, error);
            this.clients.delete(clientId);
        }
    }
    /**
     * Broadcast message to all connected clients
     */
    broadcast(message) {
        const messageStr = JSON.stringify(message);
        let sentCount = 0;
        this.clients.forEach((client, clientId) => {
            if (client.ws.readyState === ws_1.default.OPEN) {
                try {
                    client.ws.send(messageStr);
                    sentCount++;
                }
                catch (error) {
                    console.error(`Error broadcasting to client ${clientId}:`, error);
                    this.clients.delete(clientId);
                }
            }
            else {
                // Remove disconnected clients
                this.clients.delete(clientId);
            }
        });
        console.log(`Broadcasted message to ${sentCount} clients`);
    }
    /**
     * Set up data processor event handlers
     */
    setupDataProcessorHandlers() {
        this.dataProcessor.on('data-initialized', (categorizedData) => {
            console.log('Broadcasting initial data to clients');
            this.broadcast({
                type: 'data-initialized',
                payload: categorizedData,
                timestamp: Date.now()
            });
        });
        this.dataProcessor.on('data-synced', (categorizedData) => {
            console.log('Broadcasting synced data to clients');
            this.broadcast({
                type: 'data-synced',
                payload: categorizedData,
                timestamp: Date.now()
            });
        });
        this.dataProcessor.on('data-updated', (categorizedData) => {
            console.log('Broadcasting updated data to clients');
            this.broadcast({
                type: 'data-updated',
                payload: categorizedData,
                timestamp: Date.now()
            });
        });
        this.dataProcessor.on('token-added', (data) => {
            console.log(`Broadcasting new token: ${data.token.symbol}`);
            this.broadcast({
                type: 'token-added',
                payload: data,
                timestamp: Date.now()
            });
        });
        this.dataProcessor.on('token-updated', (data) => {
            console.log(`Broadcasting token update: ${data.token.symbol}`);
            this.broadcast({
                type: 'token-updated',
                payload: data,
                timestamp: Date.now()
            });
        });
        this.dataProcessor.on('token-removed', (data) => {
            console.log(`Broadcasting token removal: ${data.address}`);
            this.broadcast({
                type: 'token-removed',
                payload: data,
                timestamp: Date.now()
            });
        });
        this.dataProcessor.on('mobula-connected', () => {
            this.broadcast({
                type: 'mobula-status',
                payload: { status: 'connected' },
                timestamp: Date.now()
            });
        });
        this.dataProcessor.on('mobula-disconnected', (info) => {
            this.broadcast({
                type: 'mobula-status',
                payload: { status: 'disconnected', info },
                timestamp: Date.now()
            });
        });
        this.dataProcessor.on('processing-error', (error) => {
            this.broadcast({
                type: 'error',
                payload: { message: 'Data processing error', error: error.message },
                timestamp: Date.now()
            });
        });
    }
    /**
     * Start heartbeat mechanism to detect dead connections
     */
    startHeartbeat() {
        this.heartbeatInterval = setInterval(() => {
            this.clients.forEach((client, clientId) => {
                if (!client.isAlive) {
                    console.log(`Terminating dead connection: ${clientId}`);
                    client.ws.terminate();
                    this.clients.delete(clientId);
                    return;
                }
                client.isAlive = false;
                client.ws.ping();
            });
        }, 30000); // 30 seconds
    }
    /**
     * Generate unique client ID
     */
    generateClientId() {
        return `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    /**
     * Get server statistics
     */
    getStats() {
        return {
            isRunning: this.wss !== null,
            port: this.port,
            connectedClients: this.clients.size,
            clients: Array.from(this.clients.values()).map(client => ({
                id: client.id,
                connectedAt: client.connectedAt,
                isAlive: client.isAlive
            }))
        };
    }
    /**
     * Get number of connected clients
     */
    getClientCount() {
        return this.clients.size;
    }
}
exports.TokenWebSocketServer = TokenWebSocketServer;
