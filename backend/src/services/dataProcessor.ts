import { EventEmitter } from 'events';
import { MobulaWebSocketClient } from './mobulaClient.js';
import { DataCache } from './dataCache.js';
import { PoolData, ProcessedTokenData } from '../types/mobula.js';

export class DataProcessor extends EventEmitter {
  private mobulaClient: MobulaWebSocketClient;
  private dataCache: DataCache;
  private isProcessing = false;

  constructor(mobulaClient: MobulaWebSocketClient, dataCache: DataCache) {
    super();
    this.mobulaClient = mobulaClient;
    this.dataCache = dataCache;
    this.setupEventHandlers();
  }

  private setupEventHandlers(): void {
    // Handle Mobula WebSocket events
    this.mobulaClient.on('connected', () => {
      console.log('Data processor: Mobula client connected');
      this.emit('mobula-connected');
    });

    this.mobulaClient.on('disconnected', (info) => {
      console.log('Data processor: Mobula client disconnected', info);
      this.emit('mobula-disconnected', info);
    });

    this.mobulaClient.on('error', (error) => {
      console.error('Data processor: Mobula client error', error);
      this.emit('mobula-error', error);
    });

    // Handle data events
    this.mobulaClient.on('init', (payload) => {
      console.log('Data processor: Received initial data');
      this.handleInitData(payload);
    });

    this.mobulaClient.on('sync', (payload) => {
      console.log('Data processor: Received sync data');
      this.handleSyncData(payload);
    });

    this.mobulaClient.on('new-pool', (payload) => {
      console.log('Data processor: New pool detected');
      this.handleNewPool(payload);
    });

    this.mobulaClient.on('update-pool', (payload) => {
      console.log('Data processor: Pool updated');
      this.handleUpdatePool(payload);
    });

    this.mobulaClient.on('remove-pool', (payload) => {
      console.log('Data processor: Pool removed');
      this.handleRemovePool(payload);
    });
  }

  /**
   * Start the data processing service
   */
  public start(): void {
    console.log('Starting data processor...');
    this.isProcessing = true;
    this.mobulaClient.connect();
  }

  /**
   * Stop the data processing service
   */
  public stop(): void {
    console.log('Stopping data processor...');
    this.isProcessing = false;
    this.mobulaClient.disconnect();
  }

  /**
   * Handle initial data from Mobula
   */
  private handleInitData(payload: any): void {
    if (!this.isProcessing) return;

    try {
      console.log('Processing initial data from Mobula...');
      this.dataCache.initializeBulkData(payload);
      
      // Emit processed data to clients
      const categorizedData = this.dataCache.getCategorizedData();
      this.emit('data-initialized', categorizedData);
      
      console.log('Initial data processing complete');
    } catch (error) {
      console.error('Error processing initial data:', error);
      this.emit('processing-error', error);
    }
  }

  /**
   * Handle sync data from Mobula
   */
  private handleSyncData(payload: any): void {
    if (!this.isProcessing) return;

    try {
      console.log('Processing sync data from Mobula...');
      this.dataCache.initializeBulkData(payload);
      
      // Emit updated data to clients
      const categorizedData = this.dataCache.getCategorizedData();
      this.emit('data-synced', categorizedData);
      
      console.log('Sync data processing complete');
    } catch (error) {
      console.error('Error processing sync data:', error);
      this.emit('processing-error', error);
    }
  }

  /**
   * Handle new pool from Mobula
   */
  private handleNewPool(payload: any): void {
    if (!this.isProcessing) return;

    try {
      const poolData: PoolData = payload.pool;
      const viewName: string = payload.viewName;
      
      if (!poolData) {
        console.warn('New pool payload missing pool data');
        return;
      }

      console.log(`Processing new pool: ${poolData.tokenSymbol || 'Unknown'} in view: ${viewName}`);
      
      const processedToken = this.dataCache.updateToken(poolData);
      
      // Emit new token to clients
      this.emit('token-added', {
        token: processedToken,
        viewName,
        category: processedToken.category
      });

      // Also emit updated categorized data
      const categorizedData = this.dataCache.getCategorizedData();
      this.emit('data-updated', categorizedData);
      
    } catch (error) {
      console.error('Error processing new pool:', error);
      this.emit('processing-error', error);
    }
  }

  /**
   * Handle pool update from Mobula
   */
  private handleUpdatePool(payload: any): void {
    if (!this.isProcessing) return;

    try {
      const poolData: PoolData = payload.pool;
      const viewName: string = payload.viewName;
      
      if (!poolData) {
        console.warn('Update pool payload missing pool data');
        return;
      }

      console.log(`Processing pool update: ${poolData.tokenSymbol || 'Unknown'} in view: ${viewName}`);
      
      const processedToken = this.dataCache.updateToken(poolData);
      
      // Emit updated token to clients
      this.emit('token-updated', {
        token: processedToken,
        viewName,
        category: processedToken.category
      });

      // Also emit updated categorized data
      const categorizedData = this.dataCache.getCategorizedData();
      this.emit('data-updated', categorizedData);
      
    } catch (error) {
      console.error('Error processing pool update:', error);
      this.emit('processing-error', error);
    }
  }

  /**
   * Handle pool removal from Mobula
   */
  private handleRemovePool(payload: any): void {
    if (!this.isProcessing) return;

    try {
      const poolAddress: string = payload.poolAddress;
      const viewName: string = payload.viewName;
      
      if (!poolAddress) {
        console.warn('Remove pool payload missing pool address');
        return;
      }

      console.log(`Processing pool removal: ${poolAddress} from view: ${viewName}`);
      
      // Get token data before removal for notification
      const tokenData = this.dataCache.getToken(poolAddress);
      
      this.dataCache.removeToken(poolAddress);
      
      // Emit removal to clients
      this.emit('token-removed', {
        address: poolAddress,
        token: tokenData,
        viewName
      });

      // Also emit updated categorized data
      const categorizedData = this.dataCache.getCategorizedData();
      this.emit('data-updated', categorizedData);
      
    } catch (error) {
      console.error('Error processing pool removal:', error);
      this.emit('processing-error', error);
    }
  }

  /**
   * Get current categorized data
   */
  public getCategorizedData(): {
    new: ProcessedTokenData[];
    bonding: ProcessedTokenData[];
    bonded: ProcessedTokenData[];
  } {
    return this.dataCache.getCategorizedData();
  }

  /**
   * Get tokens by category
   */
  public getTokensByCategory(category: 'new' | 'bonding' | 'bonded'): ProcessedTokenData[] {
    return this.dataCache.getTokensByCategory(category);
  }

  /**
   * Get specific token data
   */
  public getToken(address: string): ProcessedTokenData | null {
    return this.dataCache.getToken(address);
  }

  /**
   * Get processing statistics
   */
  public getStats(): any {
    return {
      isProcessing: this.isProcessing,
      mobulaConnectionState: this.mobulaClient.getConnectionState(),
      cacheStats: this.dataCache.getCacheStats()
    };
  }

  /**
   * Check if the processor is running
   */
  public isRunning(): boolean {
    return this.isProcessing && this.mobulaClient.isConnected();
  }

  /**
   * Force refresh data (useful for testing)
   */
  public forceRefresh(): void {
    console.log('Forcing data refresh...');
    const categorizedData = this.dataCache.getCategorizedData();
    this.emit('data-updated', categorizedData);
  }
}
