import WebSocket from 'ws';
import { EventEmitter } from 'events';
import { MobulaMessage, MobulaSubscription, PoolData } from '../types/mobula.js';

export class MobulaWebSocketClient extends EventEmitter {
  private ws: WebSocket | null = null;
  private apiKey: string;
  private wsUrl: string;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 5000;
  private isConnecting = false;
  private shouldReconnect = true;

  constructor(apiKey: string, wsUrl: string) {
    super();
    this.apiKey = apiKey;
    this.wsUrl = wsUrl;
  }

  public connect(): void {
    if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.OPEN)) {
      return;
    }

    this.isConnecting = true;
    console.log('Connecting to Mobula WebSocket API...');

    try {
      this.ws = new WebSocket(this.wsUrl);

      this.ws.on('open', () => {
        console.log('Connected to Mobula WebSocket API');
        this.isConnecting = false;
        this.reconnectAttempts = 0;
        this.subscribe();
        this.emit('connected');
      });

      this.ws.on('message', (data: WebSocket.Data) => {
        try {
          const message: MobulaMessage = JSON.parse(data.toString());
          this.handleMessage(message);
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
          this.emit('error', error);
        }
      });

      this.ws.on('close', (code: number, reason: Buffer) => {
        console.log(`WebSocket connection closed: ${code} - ${reason.toString()}`);
        this.isConnecting = false;
        this.ws = null;
        this.emit('disconnected', { code, reason: reason.toString() });
        
        if (this.shouldReconnect) {
          this.scheduleReconnect();
        }
      });

      this.ws.on('error', (error: Error) => {
        console.error('WebSocket error:', error);
        this.isConnecting = false;
        this.emit('error', error);
      });

    } catch (error) {
      console.error('Failed to create WebSocket connection:', error);
      this.isConnecting = false;
      this.emit('error', error);
    }
  }

  private subscribe(): void {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      console.error('Cannot subscribe: WebSocket not connected');
      return;
    }

    const subscription: MobulaSubscription = {
      type: 'pulse-v2',
      authorization: this.apiKey,
      payload: {
        model: 'default',
        chainId: ['solana:solana'],
        poolTypes: ['pumpfun']
      }
    };

    console.log('Subscribing to Mobula Pulse Stream with filters:', {
      chainId: subscription.payload.chainId,
      poolTypes: subscription.payload.poolTypes
    });

    this.ws.send(JSON.stringify(subscription));
  }

  private handleMessage(message: MobulaMessage): void {
    console.log(`Received message type: ${message.type}`);
    
    switch (message.type) {
      case 'init':
        console.log('Received initial data from Mobula');
        this.emit('init', message.payload);
        break;
      
      case 'sync':
        console.log('Received sync data from Mobula');
        this.emit('sync', message.payload);
        break;
      
      case 'new-pool':
        console.log('New pool detected:', message.payload.pool?.pair?.token0?.symbol);
        this.emit('new-pool', message.payload);
        break;
      
      case 'update-pool':
        console.log('Pool updated:', message.payload.pool?.pair?.token0?.symbol);
        this.emit('update-pool', message.payload);
        break;
      
      case 'remove-pool':
        console.log('Pool removed:', message.payload.poolAddress);
        this.emit('remove-pool', message.payload);
        break;
      
      case 'error':
        console.error('Mobula API error:', message.payload);
        this.emit('mobula-error', message.payload);
        break;
      
      default:
        console.log('Unknown message type:', message.type);
        this.emit('unknown-message', message);
    }
  }

  private scheduleReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached. Giving up.');
      this.emit('max-reconnect-attempts');
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1); // Exponential backoff
    
    console.log(`Scheduling reconnection attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${delay}ms`);
    
    setTimeout(() => {
      if (this.shouldReconnect) {
        this.connect();
      }
    }, delay);
  }

  public disconnect(): void {
    console.log('Disconnecting from Mobula WebSocket API...');
    this.shouldReconnect = false;
    
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }

  public isConnected(): boolean {
    return this.ws !== null && this.ws.readyState === WebSocket.OPEN;
  }

  public getConnectionState(): string {
    if (!this.ws) return 'disconnected';
    
    switch (this.ws.readyState) {
      case WebSocket.CONNECTING: return 'connecting';
      case WebSocket.OPEN: return 'connected';
      case WebSocket.CLOSING: return 'closing';
      case WebSocket.CLOSED: return 'closed';
      default: return 'unknown';
    }
  }
}
