import NodeCache from 'node-cache';
import { PoolData, ProcessedTokenData } from '../types/mobula.js';

export class DataCache {
  private cache: NodeCache;
  private tokenData: Map<string, ProcessedTokenData> = new Map();
  private categorizedData: {
    new: ProcessedTokenData[];
    bonding: ProcessedTokenData[];
    bonded: ProcessedTokenData[];
  } = {
    new: [],
    bonding: [],
    bonded: []
  };

  constructor() {
    // Cache with 10 minute TTL for general data
    this.cache = new NodeCache({ 
      stdTTL: 600, // 10 minutes
      checkperiod: 60, // Check for expired keys every minute
      useClones: false // Better performance
    });

    // Set up cache event listeners
    this.cache.on('expired', (key, value) => {
      console.log(`Cache key expired: ${key}`);
    });

    this.cache.on('del', (key, value) => {
      console.log(`Cache key deleted: ${key}`);
    });
  }

  /**
   * Process raw pool data from Mobula and convert to our internal format
   */
  public processPoolData(poolData: PoolData): ProcessedTokenData {
    const token = poolData.pair.token0; // Assuming token0 is the main token
    
    const processedData: ProcessedTokenData = {
      address: token.address,
      name: poolData.tokenName || token.name || 'Unknown',
      symbol: poolData.tokenSymbol || token.symbol || 'UNKNOWN',
      image: token.logo,
      transactionCount: poolData.trades_1h || 0,
      volume: poolData.volume_1h || 0,
      marketCap: poolData.market_cap || token.marketCap || 0,
      holders: poolData.holders_count || 0,
      bondingPercentage: poolData.bondingPercentage || 0,
      category: this.determineCategory(poolData),
      createdAt: poolData.created_at,
      price: poolData.price || 0,
      priceChange24h: poolData.price_change_24h || 0
    };

    return processedData;
  }

  /**
   * Determine token category based on pool data
   */
  private determineCategory(poolData: PoolData): 'new' | 'bonding' | 'bonded' {
    // If token is bonded (completed bonding curve)
    if (poolData.bonded === true) {
      return 'bonded';
    }

    // If token has bonding curve but not yet bonded
    if (poolData.bondingCurveAddress && poolData.bondingPercentage > 0) {
      return 'bonding';
    }

    // If token is newly created (within last 24 hours) or has minimal activity
    const now = new Date();
    const createdAt = poolData.created_at ? new Date(poolData.created_at) : null;
    const isNew = createdAt && (now.getTime() - createdAt.getTime()) < 24 * 60 * 60 * 1000; // 24 hours

    if (isNew || poolData.volume_1h < 100) {
      return 'new';
    }

    // Default to bonding if has some activity
    return 'bonding';
  }

  /**
   * Add or update token data
   */
  public updateToken(poolData: PoolData): ProcessedTokenData {
    const processedData = this.processPoolData(poolData);
    const key = processedData.address;

    // Store in token map
    this.tokenData.set(key, processedData);

    // Update categorized data
    this.updateCategorizedData(processedData);

    // Cache the processed data
    this.cache.set(`token:${key}`, processedData);

    console.log(`Updated token: ${processedData.symbol} (${processedData.category})`);
    return processedData;
  }

  /**
   * Update categorized data arrays
   */
  private updateCategorizedData(tokenData: ProcessedTokenData): void {
    const { address, category } = tokenData;

    // Remove from all categories first
    Object.keys(this.categorizedData).forEach(cat => {
      const categoryKey = cat as keyof typeof this.categorizedData;
      this.categorizedData[categoryKey] = this.categorizedData[categoryKey].filter(
        token => token.address !== address
      );
    });

    // Add to appropriate category
    this.categorizedData[category].push(tokenData);

    // Sort each category
    this.sortCategorizedData();

    // Limit each category to 50 items (as per Mobula default)
    Object.keys(this.categorizedData).forEach(cat => {
      const categoryKey = cat as keyof typeof this.categorizedData;
      if (this.categorizedData[categoryKey].length > 50) {
        this.categorizedData[categoryKey] = this.categorizedData[categoryKey].slice(0, 50);
      }
    });
  }

  /**
   * Sort categorized data based on appropriate criteria
   */
  private sortCategorizedData(): void {
    // Sort 'new' by creation date (newest first)
    this.categorizedData.new.sort((a, b) => {
      if (!a.createdAt && !b.createdAt) return 0;
      if (!a.createdAt) return 1;
      if (!b.createdAt) return -1;
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    });

    // Sort 'bonding' by market cap (highest first)
    this.categorizedData.bonding.sort((a, b) => b.marketCap - a.marketCap);

    // Sort 'bonded' by creation date (newest first)
    this.categorizedData.bonded.sort((a, b) => {
      if (!a.createdAt && !b.createdAt) return 0;
      if (!a.createdAt) return 1;
      if (!b.createdAt) return -1;
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    });
  }

  /**
   * Remove token from cache and categorized data
   */
  public removeToken(address: string): void {
    // Remove from token map
    this.tokenData.delete(address);

    // Remove from categorized data
    Object.keys(this.categorizedData).forEach(cat => {
      const categoryKey = cat as keyof typeof this.categorizedData;
      this.categorizedData[categoryKey] = this.categorizedData[categoryKey].filter(
        token => token.address !== address
      );
    });

    // Remove from cache
    this.cache.del(`token:${address}`);

    console.log(`Removed token: ${address}`);
  }

  /**
   * Get token by address
   */
  public getToken(address: string): ProcessedTokenData | null {
    return this.tokenData.get(address) || null;
  }

  /**
   * Get all categorized data
   */
  public getCategorizedData(): typeof this.categorizedData {
    return {
      new: [...this.categorizedData.new],
      bonding: [...this.categorizedData.bonding],
      bonded: [...this.categorizedData.bonded]
    };
  }

  /**
   * Get tokens by category
   */
  public getTokensByCategory(category: 'new' | 'bonding' | 'bonded'): ProcessedTokenData[] {
    return [...this.categorizedData[category]];
  }

  /**
   * Initialize with bulk data (for init/sync messages)
   */
  public initializeBulkData(viewData: any): void {
    console.log('Initializing bulk data from Mobula...');
    
    // Process each view's data
    Object.keys(viewData).forEach(viewName => {
      const data = viewData[viewName]?.data || [];
      console.log(`Processing ${data.length} tokens for view: ${viewName}`);
      
      data.forEach((poolData: PoolData) => {
        try {
          this.updateToken(poolData);
        } catch (error) {
          console.error('Error processing pool data:', error);
        }
      });
    });

    console.log('Bulk data initialization complete');
    this.logCacheStats();
  }

  /**
   * Get cache statistics
   */
  public getCacheStats(): any {
    return {
      totalTokens: this.tokenData.size,
      categoryCounts: {
        new: this.categorizedData.new.length,
        bonding: this.categorizedData.bonding.length,
        bonded: this.categorizedData.bonded.length
      },
      cacheKeys: this.cache.keys().length,
      cacheStats: this.cache.getStats()
    };
  }

  /**
   * Log cache statistics
   */
  public logCacheStats(): void {
    const stats = this.getCacheStats();
    console.log('Cache Statistics:', JSON.stringify(stats, null, 2));
  }

  /**
   * Clear all cache data
   */
  public clearAll(): void {
    this.tokenData.clear();
    this.categorizedData = { new: [], bonding: [], bonded: [] };
    this.cache.flushAll();
    console.log('Cache cleared');
  }
}
