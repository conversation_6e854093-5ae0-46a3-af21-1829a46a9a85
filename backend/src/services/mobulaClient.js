"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MobulaWebSocketClient = void 0;
const ws_1 = __importDefault(require("ws"));
const events_1 = require("events");
class MobulaWebSocketClient extends events_1.EventEmitter {
    constructor(apiKey, wsUrl) {
        super();
        this.ws = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 5000;
        this.isConnecting = false;
        this.shouldReconnect = true;
        this.apiKey = apiKey;
        this.wsUrl = wsUrl;
    }
    connect() {
        if (this.isConnecting || (this.ws && this.ws.readyState === ws_1.default.OPEN)) {
            return;
        }
        this.isConnecting = true;
        console.log('Connecting to Mobula WebSocket API...');
        try {
            this.ws = new ws_1.default(this.wsUrl);
            this.ws.on('open', () => {
                console.log('Connected to Mobula WebSocket API');
                this.isConnecting = false;
                this.reconnectAttempts = 0;
                this.subscribe();
                this.emit('connected');
            });
            this.ws.on('message', (data) => {
                try {
                    const message = JSON.parse(data.toString());
                    this.handleMessage(message);
                }
                catch (error) {
                    console.error('Error parsing WebSocket message:', error);
                    this.emit('error', error);
                }
            });
            this.ws.on('close', (code, reason) => {
                console.log(`WebSocket connection closed: ${code} - ${reason.toString()}`);
                this.isConnecting = false;
                this.ws = null;
                this.emit('disconnected', { code, reason: reason.toString() });
                if (this.shouldReconnect) {
                    this.scheduleReconnect();
                }
            });
            this.ws.on('error', (error) => {
                console.error('WebSocket error:', error);
                this.isConnecting = false;
                this.emit('error', error);
            });
        }
        catch (error) {
            console.error('Failed to create WebSocket connection:', error);
            this.isConnecting = false;
            this.emit('error', error);
        }
    }
    subscribe() {
        if (!this.ws || this.ws.readyState !== ws_1.default.OPEN) {
            console.error('Cannot subscribe: WebSocket not connected');
            return;
        }
        const subscription = {
            type: 'pulse-v2',
            authorization: this.apiKey,
            payload: {
                model: 'default',
                chainId: ['solana:solana'],
                poolTypes: ['pumpfun']
            }
        };
        console.log('Subscribing to Mobula Pulse Stream with filters:', {
            chainId: subscription.payload.chainId,
            poolTypes: subscription.payload.poolTypes
        });
        this.ws.send(JSON.stringify(subscription));
    }
    handleMessage(message) {
        var _a, _b, _c, _d, _e, _f;
        console.log(`Received message type: ${message.type}`);
        switch (message.type) {
            case 'init':
                console.log('Received initial data from Mobula');
                this.emit('init', message.payload);
                break;
            case 'sync':
                console.log('Received sync data from Mobula');
                this.emit('sync', message.payload);
                break;
            case 'new-pool':
                console.log('New pool detected:', (_c = (_b = (_a = message.payload.pool) === null || _a === void 0 ? void 0 : _a.pair) === null || _b === void 0 ? void 0 : _b.token0) === null || _c === void 0 ? void 0 : _c.symbol);
                this.emit('new-pool', message.payload);
                break;
            case 'update-pool':
                console.log('Pool updated:', (_f = (_e = (_d = message.payload.pool) === null || _d === void 0 ? void 0 : _d.pair) === null || _e === void 0 ? void 0 : _e.token0) === null || _f === void 0 ? void 0 : _f.symbol);
                this.emit('update-pool', message.payload);
                break;
            case 'remove-pool':
                console.log('Pool removed:', message.payload.poolAddress);
                this.emit('remove-pool', message.payload);
                break;
            case 'error':
                console.error('Mobula API error:', message.payload);
                this.emit('mobula-error', message.payload);
                break;
            default:
                console.log('Unknown message type:', message.type);
                this.emit('unknown-message', message);
        }
    }
    scheduleReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.error('Max reconnection attempts reached. Giving up.');
            this.emit('max-reconnect-attempts');
            return;
        }
        this.reconnectAttempts++;
        const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1); // Exponential backoff
        console.log(`Scheduling reconnection attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${delay}ms`);
        setTimeout(() => {
            if (this.shouldReconnect) {
                this.connect();
            }
        }, delay);
    }
    disconnect() {
        console.log('Disconnecting from Mobula WebSocket API...');
        this.shouldReconnect = false;
        if (this.ws) {
            this.ws.close();
            this.ws = null;
        }
    }
    isConnected() {
        return this.ws !== null && this.ws.readyState === ws_1.default.OPEN;
    }
    getConnectionState() {
        if (!this.ws)
            return 'disconnected';
        switch (this.ws.readyState) {
            case ws_1.default.CONNECTING: return 'connecting';
            case ws_1.default.OPEN: return 'connected';
            case ws_1.default.CLOSING: return 'closing';
            case ws_1.default.CLOSED: return 'closed';
            default: return 'unknown';
        }
    }
}
exports.MobulaWebSocketClient = MobulaWebSocketClient;
