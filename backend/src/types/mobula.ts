// Mobula API Types based on the documentation

export interface Token {
  address: string;
  price: number;
  priceToken: number;
  priceTokenString: string;
  approximateReserveUSD: number;
  approximateReserveTokenRaw: string;
  approximateReserveToken: number;
  symbol: string;
  name: string;
  id: number | null;
  decimals: number;
  totalSupply: number;
  circulatingSupply: number;
  logo: string | null;
  chainId: string;
  marketCap?: number;
  marketCapDiluted?: number;
}

export interface Pair {
  token0: Token;
  token1: Token;
  volume24h: number;
  liquidity: number;
  blockchain: string;
  address: string;
  createdAt: Date | null;
  type: string;
  baseToken: string;
  exchange: {
    name: string;
    logo: string;
  };
  factory: string | null;
  quoteToken: string;
  price: number;
  priceToken: number;
  priceTokenString: string;
  bonded?: boolean;
  bondingPercentage?: number;
  bondingCurveAddress?: string | null;
}

export interface PoolData {
  // Price Data
  price: number;
  price_change_1min: number;
  price_change_5min: number;
  price_change_1h: number;
  price_change_4h: number;
  price_change_6h: number;
  price_change_12h: number;
  price_change_24h: number;
  
  // Market Data
  market_cap: number;
  created_at: Date | null;
  holders_count: number;
  
  // Volume Data
  volume_1min: number;
  volume_5min: number;
  volume_15min: number;
  volume_1h: number;
  volume_4h: number;
  volume_6h: number;
  volume_12h: number;
  volume_24h: number;
  
  // Trading Activity
  trades_1min: number;
  trades_5min: number;
  trades_15min: number;
  trades_1h: number;
  trades_4h: number;
  trades_6h: number;
  trades_12h: number;
  trades_24h: number;
  
  // Buy/Sell Breakdown
  buys_1min: number;
  buys_5min: number;
  buys_15min: number;
  buys_1h: number;
  buys_4h: number;
  buys_6h: number;
  buys_12h: number;
  buys_24h: number;
  
  sells_1min: number;
  sells_5min: number;
  sells_15min: number;
  sells_1h: number;
  sells_4h: number;
  sells_6h: number;
  sells_12h: number;
  sells_24h: number;
  
  // Unique Participants
  buyers_1min: number;
  buyers_5min: number;
  buyers_15min: number;
  buyers_1h: number;
  buyers_4h: number;
  buyers_6h: number;
  buyers_12h: number;
  buyers_24h: number;
  
  sellers_1min: number;
  sellers_5min: number;
  sellers_15min: number;
  sellers_1h: number;
  sellers_4h: number;
  sellers_6h: number;
  sellers_12h: number;
  sellers_24h: number;
  
  // Traders
  traders_1min: number;
  traders_5min: number;
  traders_15min: number;
  traders_1h: number;
  traders_4h: number;
  traders_6h: number;
  traders_12h: number;
  traders_24h: number;
  
  // Fees Paid
  fees_paid_1min: number;
  fees_paid_5min: number;
  fees_paid_15min: number;
  fees_paid_1h: number;
  fees_paid_4h: number;
  fees_paid_6h: number;
  fees_paid_12h: number;
  fees_paid_24h: number;
  
  // Token Information
  source: string | null;
  deployer: string | null;
  tokenSymbol: string | null;
  tokenName: string | null;
  dexscreenerListed: boolean | null;
  description: string | null;
  
  // Holdings Analysis
  devHoldings: number;
  insidersHoldings: number;
  snipersHoldings: number;
  bundlersHoldings: number;
  deployerMigrations: number;
  twitterReusesCount: number;
  proTradersHolding: number;
  top10Holdings: number;
  top50Holdings: number;
  top200Holdings: number;
  
  // Pair Information
  pair: Pair;
  bondingPercentage: number;
  bonded: boolean;
  bondingCurveAddress: string | null;
  sourceFactory: string | null;
  socials: {
    twitter: string | null;
    website: string | null;
    telegram: string | null;
    others: Record<string, unknown> | null;
  };
  top10percent: number;
  devHolder: number;
  holders_list: Array<{
    address: string;
    balance: number;
    nativeBalance: number;
    balanceUsd: number;
    boughtAmount: number;
    soldAmount: number;
    pnl: number;
  }>;
}

export interface MobulaMessage {
  type: 'init' | 'sync' | 'new-pool' | 'update-pool' | 'remove-pool' | 'error';
  payload: any;
}

export interface MobulaSubscription {
  type: 'pulse-v2';
  authorization: string;
  payload: {
    model: 'default';
    chainId: string[];
    poolTypes: string[];
  };
}

export interface ProcessedTokenData {
  address: string;
  name: string;
  symbol: string;
  image: string | null;
  transactionCount: number;
  volume: number;
  marketCap: number;
  holders: number;
  bondingPercentage: number;
  category: 'new' | 'bonding' | 'bonded';
  createdAt: Date | null;
  price: number;
  priceChange24h: number;
}
